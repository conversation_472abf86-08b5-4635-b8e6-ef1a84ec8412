import { Ava<PERSON>, Button, Flex, Form, Popover, Radio, Space } from "antd";
import { forwardRef, useImperativeHandle, useState } from "react";
import TextArea from "antd/es/input/TextArea";
import useUserInfoStore from "@/app/store/userInfo";
import { InterviewDimension, ResumeResultReq } from "@/app/store/modules/candidate";
import { getInterviewEvaluationTemplateApi, saveResumeResultApi } from "@/app/request/modules/candidate";
import { RespParams } from "@/app/typing";
import messageService from "@/app/lib/message";
import { EVALUATE_TYPE } from "@/app/constant";
import styles from "./InterviewEvaluationPopover.module.scss";

export interface InterviewEvaluationPopoverExpose {
    showPopover: (
        type: EVALUATE_TYPE,
        templateId: string,
        applicantId: string,
        stageId: string,
        jobId: string,
        initValue?: string
    ) => void;
}

interface InterviewEvaluationPopoverProps {
    refresh: () => void;
    children: React.ReactNode;
}

const InterviewEvaluationPopover = forwardRef<InterviewEvaluationPopoverExpose, InterviewEvaluationPopoverProps>(
    (props, ref) => {
        const { refresh, children } = props;
        const currentUser = useUserInfoStore((state) => state.user);
        const [form] = Form.useForm();

        const [open, setOpen] = useState<boolean>(false);
        const [loading, setLoading] = useState<boolean>(false);
        const [interviewType, setInterviewType] = useState<number>(1);
        const [jobId, setJobId] = useState<string>("");
        const [templateId, setTemplateId] = useState<string>("");
        const [applicantId, setApplicantId] = useState<string>("");
        const [stageId, setStageId] = useState<string>("");
        const [detailList, setDetailList] = useState<InterviewDimension[]>([]);

        useImperativeHandle(ref, () => ({
            showPopover: async (type, templateId, applicantId, stageId, jobId, initValue) => {
                setDetailList([]);
                setInterviewType(type);
                setTemplateId(templateId);
                setApplicantId(applicantId);
                setStageId(stageId);
                setJobId(jobId);
                form.resetFields();

                if (type === EVALUATE_TYPE.first) {
                    const dimensions: InterviewDimension[] = [
                        {
                            dimensionId: "简历初筛结论",
                            weight: 1,
                            isConclusion: true,
                            description: "",
                            questions: [
                                {
                                    questionId: "简历初筛结果",
                                    type: "1",
                                    isRequired: true,
                                    options: [
                                        { value: "通过", score: 1 },
                                        { value: "淘汰", score: 1 },
                                    ],
                                },
                                {
                                    questionId: "简历初筛评价",
                                    type: "3",
                                    isRequired: false,
                                },
                            ],
                        },
                    ];
                    setDetailList(dimensions);

                    if (initValue) {
                        form.setFieldsValue({
                            简历初筛结果: initValue,
                        });
                    }
                } else {
                    if (templateId) {
                        try {
                            const res: RespParams<any> = await (
                                await getInterviewEvaluationTemplateApi(templateId)
                            ).json();

                            if (res.code === 200) {
                                setDetailList(res.data.detail);
                            }
                        } catch (error) {
                            console.error("获取面试模板失败:", error);
                            setDetailList([]);
                        }
                    } else {
                        setDetailList([]);
                    }
                }
            },
        }));

        const handleSubmit = async () => {
            try {
                const values = await form.validateFields();
                setLoading(true);

                const params: ResumeResultReq = {
                    templateId: templateId,
                    applicantId: applicantId,
                    stageId: stageId,
                    isPreliminary: interviewType === EVALUATE_TYPE.first,
                    jobId: jobId,
                    evaluationContent: {
                        dimensions: detailList.map((item) => ({
                            ...item,
                            questions: item.questions.map((question) => ({
                                ...question,
                                selectedOptions:
                                    question.type === "1" && values[question.questionId]
                                        ? [{ optionDescription: values[question.questionId], score: 1 }]
                                        : question.selectedOptions || [],
                            })),
                        })),
                    },
                    interviewerInfo: {
                        interviewerId: currentUser?.emp_id || "",
                        avatarUrl: currentUser?.avatar || "",
                        name: currentUser?.emp_name || "",
                    },
                };

                // Add text area responses to dimensions
                Object.keys(values).forEach((key) => {
                    const dimension = detailList.find((d) =>
                        d.questions.some((q) => q.questionId === key && q.type === "3")
                    );
                    if (dimension) {
                        const question = dimension.questions.find((q) => q.questionId === key);
                        if (question && values[key]) {
                            question.description = values[key];
                        }
                    }
                });

                const res: RespParams<any> = await (await saveResumeResultApi(params)).json();

                if (res.code === 200) {
                    messageService.success("评价提交成功");
                    setOpen(false);
                    refresh();
                } else {
                    messageService.error(res.msg || "提交失败");
                }
            } catch (error) {
                console.error("提交评价失败:", error);
                messageService.error("提交失败");
            } finally {
                setLoading(false);
            }
        };

        const handleCancel = () => {
            setOpen(false);
            form.resetFields();
        };

        const renderFormItem = (question: any) => {
            switch (question.type) {
                case "1":
                    return (
                        <Radio.Group
                            options={question.options?.map((option: any) => ({
                                label: option.value,
                                value: option.value,
                            }))}
                        />
                    );
                case "3":
                    return <TextArea placeholder="请输入评价内容" allowClear showCount maxLength={5000} rows={3} />;
                default:
                    return <div>未知类型</div>;
            }
        };

        const content = (
            <div className={styles["popover-content"]}>
                <div className={styles["header"]}>
                    <Flex align="center" gap={8}>
                        <Avatar src={currentUser?.avatar} alt="用户头像" size="small" />
                        <span className={styles["user-name"]}>
                            {currentUser?.emp_id}-{currentUser?.emp_name}
                        </span>
                    </Flex>
                </div>

                <Form form={form} layout="vertical" className={styles["form"]}>
                    {detailList.map((dimension) => (
                        <div key={dimension.dimensionId} className={styles["dimension"]}>
                            {dimension.questions.map((question) => (
                                <Form.Item
                                    key={question.questionId}
                                    name={question.questionId}
                                    label={question.questionId}
                                    rules={[{ required: question.isRequired, message: "此项为必填项" }]}
                                    className={styles["form-item"]}
                                >
                                    {renderFormItem(question)}
                                </Form.Item>
                            ))}
                        </div>
                    ))}
                </Form>

                <div className={styles["footer"]}>
                    <Space>
                        <Button onClick={handleCancel} disabled={loading}>
                            取消
                        </Button>
                        <Button type="primary" onClick={handleSubmit} loading={loading}>
                            确认提交
                        </Button>
                    </Space>
                </div>
            </div>
        );

        return (
            <Popover
                content={content}
                title="填写评价"
                trigger="click"
                open={open}
                onOpenChange={setOpen}
                placement="bottomLeft"
            >
                {children}
            </Popover>
        );
    }
);

InterviewEvaluationPopover.displayName = "InterviewEvaluationPopover";

export default InterviewEvaluationPopover;
