// Global styles for the popover
:global(.ant-popover) {
    .ant-popover-content {
        padding: 0;
    }

    .ant-popover-inner {
        padding: 0;
        border-radius: 8px;
        box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    }

    .ant-popover-title {
        padding: 16px 16px 0 16px;
        margin-bottom: 0;
        font-weight: 600;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 12px;
    }
}

.popover-content {
    width: 400px;
    max-height: 500px;
    overflow-y: auto;
}

.header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafafa;
    
    .user-name {
        font-size: 14px;
        color: #666;
    }
}

.form {
    padding: 16px;
    
    .dimension {
        margin-bottom: 16px;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
    
    .form-item {
        margin-bottom: 16px;
        
        &:last-child {
            margin-bottom: 0;
        }
        
        :global(.ant-form-item-label) {
            padding-bottom: 4px;
            
            > label {
                font-weight: 500;
                color: #262626;
            }
        }
        
        :global(.ant-radio-group) {
            display: flex;
            flex-direction: column;
            gap: 8px;
            
            .ant-radio-wrapper {
                margin-right: 0;
            }
        }
        
        :global(.ant-input) {
            border-radius: 6px;
        }
        
        :global(.ant-input:focus),
        :global(.ant-input-focused) {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
    }
}

.footer {
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;
    background-color: #fafafa;
    display: flex;
    justify-content: flex-end;
    
    :global(.ant-btn) {
        border-radius: 6px;
    }
    
    :global(.ant-btn-primary) {
        background-color: #1890ff;
        border-color: #1890ff;
        
        &:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
    }
}

// Responsive design for smaller screens
@media (max-width: 768px) {
    .popover-content {
        width: 320px;
        max-height: 400px;
    }
}
