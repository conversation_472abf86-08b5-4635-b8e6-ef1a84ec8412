.candidate-wrapper {
    height: 100%;
    display: flex;
    flex-direction: row;

    .sidebar {
        transition: all 0.3s ease;
        overflow: hidden;

        &--collapsed {
            .ant-layout-sider-children {
                padding: 8px 4px;
            }
        }
    }

    .collapsed-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
        padding: 8px 4px;

        .collapsed-header {
            margin-bottom: 12px;

            .expand-button {
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 6px;
                transition: all 0.2s ease;

                &:hover {
                    background-color: var(--hover-color);
                }
            }
        }

        .collapsed-job-card {
            width: 60px;
            height: 60px;
            background: var(--primary);
            color: white;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

                &::after {
                    content: "点击展开";
                    position: absolute;
                    top: -30px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 10px;
                    white-space: nowrap;
                    z-index: 1000;
                }
            }

            .job-title {
                font-size: 10px;
                font-weight: 500;
                line-height: 1.2;
                margin-bottom: 4px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                width: 100%;
                padding: 0 4px;
            }

            .job-count {
                font-size: 12px;
                font-weight: 600;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 2px 6px;
                min-width: 20px;
                line-height: 1;
            }
        }
    }

    .candidate-tree {
        margin: 8px 0;
        display: flex;
        flex-direction: column;
        gap: 12px;

        :global {
            .ant-tree-node-content-wrapper {
                max-width: 80%;
                overflow: hidden;
            }
        }
    }

    .badge-wrapper {
        position: absolute;
        top: 0;
        right: -15px;
    }

    .candidate-main-wrapper {
        width: 100%;
        height: 100%;
        position: relative;

        .candidate-main__header {
            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;

                font-size: 18px;
                color: var(--main-text-color);
                font-weight: 600;
            }
        }

        .candidate-main__content {
            overflow: auto;
            margin: 16px 0;
            border-top: 1px solid var(--border-color);
            border-bottom: 1px solid var(--border-color);
        }
    }
}

.filter-popover {
    :global {
        .ant-popover-inner {
            width: 300px;
            padding: 16px;
        }
    }
}

.schedule-interview-wrapper {
    .main-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--main-text-color);
        margin-bottom: 20px;
    }

    .select-interviewer-button {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        margin: 8px 0;
        color: var(--primary);
        background-color: var(--primary-bg-color);
        svg {
            margin-right: 4px;
        }
    }

    .hint-message {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        line-height: 12px;
        color: var(--sub-text-color-2);
    }

    .interviewer-list {
        display: grid;
        grid-template-columns: repeat(3, minmax(0, 1fr));
        gap: 12px;
        margin-top: 12px;
        margin-bottom: 20px;
    }

    .interview-review {
        width: 50%;
        .label-name {
            margin-bottom: 8px;
            color: var(--main-text-color);
        }
    }
}

.interviewer-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: 12px;
    background: var(--bg-color);

    .name {
        color: var(--main-text-color);
    }

    .time {
        font-size: 12px;
        color: var(--sub-text-color);
    }
}

.interview-evaluation-wrapper {
    .interview-evaluation__title {
        font-size: 16px;
        font-weight: 600;
        margin-top: 12px;
        color: var(--main-text-color);
    }

    .interview-evaluation__content {
        padding: 12px;
    }
}

.interview-question-wrapper {
    display: flex;
    flex-direction: column;
    gap: 16px;
    .interview-dimension {
        .title {
            color: var(--main-text-color);
            font-size: 16px;
            font-weight: 600;
        }
        .desc {
            color: var(--sub-text-color-2);
            margin-top: 4px;
        }

        .question-wrapper {
            padding: 16px;
        }
    }
}
